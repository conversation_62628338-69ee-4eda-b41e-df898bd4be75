{"active": false, "connections": {"/": {"main": [[{"node": "Globals", "type": "main", "index": 0}]]}, "n8n": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "tag?": {"main": [[{"node": "/", "type": "main", "index": 0}], [{"node": "Globals", "type": "main", "index": 0}]]}, "Globals": {"main": [[{"node": "Get file data", "type": "main", "index": 0}]]}, "Get File": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "File is new": {"main": [[{"node": "Create new file", "type": "main", "index": 0}]]}, "Merge Items": {"main": [[{"node": "isDiffOrNew", "type": "main", "index": 0}]]}, "isDiffOrNew": {"main": [[{"node": "Check Status", "type": "main", "index": 0}]]}, "Check Status": {"main": [[{"node": "Same file - Do nothing", "type": "main", "index": 0}], [{"node": "File is different", "type": "main", "index": 0}], [{"node": "File is new", "type": "main", "index": 0}]]}, "Get file data": {"main": [[{"node": "If file too large", "type": "main", "index": 0}]]}, "Create new file": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[], [{"node": "Execute Workflow", "type": "main", "index": 0}]]}, "Execute Workflow": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "File is different": {"main": [[{"node": "Edit existing file", "type": "main", "index": 0}]]}, "If file too large": {"main": [[{"node": "Get File", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Edit existing file": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "On clicking 'execute'": {"main": [[{"node": "n8n", "type": "main", "index": 0}]]}, "Same file - Do nothing": {"main": [[{"node": "Return", "type": "main", "index": 0}]]}, "Execute Workflow Trigger": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}, {"node": "tag?", "type": "main", "index": 0}]]}}, "createdAt": "2025-06-15T00:46:21.505Z", "id": "TpVRz4OZTjlTeQdz", "isArchived": false, "meta": {"templateCredsSetupCompleted": true}, "name": "N8N-BackuptoGitHub", "nodes": [{"parameters": {}, "id": "051a643f-1d23-4d1b-83cd-65e3a92d72c0", "name": "On clicking 'execute'", "type": "n8n-nodes-base.manualTrigger", "position": [440, 260], "typeVersion": 1}, {"parameters": {"content": "## Subworkflow", "height": 751, "width": 2547, "color": 6}, "id": "f869a607-17c0-465c-baf0-7e94d135430c", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.stickyNote", "position": [0, 640], "typeVersion": 1}, {"parameters": {"filters": {}, "requestOptions": {}}, "id": "92b70a74-0847-4281-a96c-8e010b70eb1c", "name": "n8n", "type": "n8n-nodes-base.n8n", "position": [700, 340], "typeVersion": 1, "credentials": {"n8nApi": {"id": "YvdOyzkD603QCDx3", "name": "n8n account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "8d513345-6484-431f-afb7-7cf045c90f4f", "name": "Done", "type": "boolean", "value": true}]}, "options": {}}, "id": "a7b43820-434b-4960-80b6-b5ab58b29f3a", "name": "Return", "type": "n8n-nodes-base.set", "position": [2340, 840], "typeVersion": 3.3}, {"parameters": {"url": "={{ $json.download_url }}", "options": {}}, "id": "********-6328-4856-ab2c-e9cd349061c2", "name": "Get File", "type": "n8n-nodes-base.httpRequest", "position": [1440, 720], "typeVersion": 4.2}, {"parameters": {"conditions": {"options": {"version": 1, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "45ce825e-9fa6-430c-8931-9aaf22c42585", "operator": {"type": "string", "operation": "empty", "singleValue": true}, "leftValue": "={{ $json.content }}", "rightValue": ""}, {"id": "9619a55f-7fb1-4f24-b1a7-7aeb82365806", "operator": {"type": "string", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.error }}", "rightValue": ""}]}, "options": {}}, "id": "af12da31-440d-4985-a9af-237238061d46", "name": "If file too large", "type": "n8n-nodes-base.if", "position": [1240, 740], "typeVersion": 2}, {"parameters": {}, "id": "85eb2a98-4537-432e-8c90-8eda43687297", "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "position": [1240, 1000], "typeVersion": 2}, {"parameters": {"jsCode": "const orderJsonKeys = (jsonObj) => {\n  const ordered = {};\n  Object.keys(jsonObj).sort().forEach(key => {\n    ordered[key] = jsonObj[key];\n  });\n  return ordered;\n}\n\n// Check if file returned with content\nif (Object.keys($input.all()[0].json).includes(\"content\")) {\n  // Decode base64 content and parse JSON\n  const origWorkflow = JSON.parse(Buffer.from($input.all()[0].json.content, 'base64').toString());\n  const n8nWorkflow = $input.all()[1].json;\n  \n  // Order JSON objects\n  const orderedOriginal = orderJsonKeys(origWorkflow);\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n\n  // Determine difference\n  if (JSON.stringify(orderedOriginal) === JSON.stringify(orderedActual)) {\n    $input.all()[0].json.github_status = \"same\";\n  } else {\n    $input.all()[0].json.github_status = \"different\";\n    $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n  }\n  $input.all()[0].json.content_decoded = orderedOriginal;\n// No file returned / new workflow\n} else if (Object.keys($input.all()[0].json).includes(\"data\")) {\n  const origWorkflow = JSON.parse($input.all()[0].json.data);\n  const n8nWorkflow = $input.all()[1].json;\n  \n  // Order JSON objects\n  const orderedOriginal = orderJsonKeys(origWorkflow);\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n\n  // Determine difference\n  if (JSON.stringify(orderedOriginal) === JSON.stringify(orderedActual)) {\n    $input.all()[0].json.github_status = \"same\";\n  } else {\n    $input.all()[0].json.github_status = \"different\";\n    $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n  }\n  $input.all()[0].json.content_decoded = orderedOriginal;\n\n} else {\n  // Order JSON object\n  const n8nWorkflow = $input.all()[1].json;\n  const orderedActual = orderJsonKeys(n8nWorkflow);\n  \n  // Proper formatting\n  $input.all()[0].json.github_status = \"new\";\n  $input.all()[0].json.n8n_data_stringy = JSON.stringify(orderedActual, null, 2);\n}\n\n// Return items\nreturn $input.all();"}, "id": "879380a2-2c56-4a62-90b5-d85c86043270", "name": "isDiffOrNew", "type": "n8n-nodes-base.code", "position": [1440, 1000], "typeVersion": 1}, {"parameters": {"dataType": "string", "value1": "={{$json.github_status}}", "rules": {"rules": [{"value2": "same"}, {"value2": "different", "output": 1}, {"value2": "new", "output": 2}]}}, "id": "4952e8c7-3350-42b7-953b-bbaa886c107a", "name": "Check Status", "type": "n8n-nodes-base.switch", "position": [1660, 1000], "typeVersion": 1}, {"parameters": {}, "id": "28b4e383-af86-4a09-9add-065b52655359", "name": "Same file - Do nothing", "type": "n8n-nodes-base.noOp", "position": [1880, 840], "typeVersion": 1}, {"parameters": {}, "id": "efc2a14d-b0fa-4e47-8717-16184a47e25b", "name": "File is different", "type": "n8n-nodes-base.noOp", "position": [1880, 1000], "typeVersion": 1}, {"parameters": {}, "id": "a935a3e0-47ca-4dbe-8ce7-97c4506503ac", "name": "File is new", "type": "n8n-nodes-base.noOp", "position": [1880, 1160], "typeVersion": 1}, {"parameters": {"resource": "file", "owner": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.owner }}"}, "repository": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.name }}"}, "filePath": "={{ $('Globals').item.json.repo.path }}{{$('Execute Workflow Trigger').first().json.id}}.json", "fileContent": "={{$('isDiffOrNew').item.json[\"n8n_data_stringy\"]}}", "commitMessage": "={{$('Execute Workflow Trigger').first().json.name}} ({{$json.github_status}})"}, "id": "767acb81-05a5-4374-9f37-fca8af3a34c9", "name": "Create new file", "type": "n8n-nodes-base.github", "position": [2100, 1160], "typeVersion": 1, "webhookId": "74848efd-745a-473b-8dd0-c965e1bb731c", "credentials": {"githubApi": {"id": "Qtgltkkid3SAmtdp", "name": "GitHub account"}}}, {"parameters": {"resource": "file", "operation": "edit", "owner": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.owner }}"}, "repository": {"__rl": true, "mode": "name", "value": "={{ $('Globals').item.json.repo.name }}"}, "filePath": "={{ $('Globals').item.json.repo.path }}{{$('Execute Workflow Trigger').first().json.id}}.json", "fileContent": "={{$('isDiffOrNew').item.json[\"n8n_data_stringy\"]}}", "commitMessage": "={{$('Execute Workflow Trigger').first().json.name}} ({{$json.github_status}})"}, "id": "7cc8bd84-f5e3-4acf-b498-ba6d9b6f2425", "name": "Edit existing file", "type": "n8n-nodes-base.github", "position": [2100, 980], "typeVersion": 1, "webhookId": "be7bf6df-71b7-49b4-a838-a626a3e2fbda", "credentials": {"githubApi": {"id": "Qtgltkkid3SAmtdp", "name": "GitHub account"}}}, {"parameters": {"options": {}}, "id": "0b74906d-17a2-45d7-8d65-0e850d198e49", "name": "Loop Over Items", "type": "n8n-nodes-base.splitInBatches", "position": [900, 360], "typeVersion": 3}, {"parameters": {"rule": {"interval": [{"triggerAtHour": 7}]}}, "id": "a2650754-e869-4acc-a8c6-289f9c3aed0a", "name": "Schedule Trigger", "type": "n8n-nodes-base.scheduleTrigger", "position": [440, 460], "typeVersion": 1.2}, {"parameters": {"content": "## Backup to GitHub \nThis workflow will backup all instance workflows to GitHub.\n\nThe files are saved `ID.json` for the filename.\n\n### Setup\nOpen `Globals` node and update the values below 👇\n\n- **repo.owner:** your Github username\n- **repo.name:** the name of your repository\n- **repo.path:** the folder to use within the repository. If it doesn't exist it will be created.\n\n\nIf your username was `john-doe` and your repository was called `n8n-backups` and you wanted the workflows to go into a `workflows` folder you would set:\n\n- repo.owner - john-doe\n- repo.name - n8n-backups\n- repo.path - workflows/\n\n\nThe workflow calls itself using a subworkflow, to help reduce memory usage.", "height": 600.88409546716, "width": 371.1995072042308, "color": 4}, "id": "fc421d6e-39e6-426d-b6b6-13dc3451b3a0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1}, {"parameters": {"content": "## Main workflow loop", "height": 434.88564057365943, "width": 886.4410237965205, "color": 7}, "id": "fcfdfeef-cb70-4dbf-b0c3-dca68ca8cd72", "name": "Sticky Note2", "type": "n8n-nodes-base.stickyNote", "position": [400, 180], "typeVersion": 1}, {"parameters": {"resource": "file", "operation": "get", "owner": {"__rl": true, "mode": "name", "value": "={{ $json.repo.owner }}"}, "repository": {"__rl": true, "mode": "name", "value": "={{ $json.repo.name }}"}, "filePath": "={{ $json.repo.path }}{{ $('Execute Workflow Trigger').item.json.id }}.json", "asBinaryProperty": false, "additionalParameters": {}}, "id": "c781e567-0972-4ad6-b9e3-7db46501be0a", "name": "Get file data", "type": "n8n-nodes-base.github", "position": [1040, 740], "typeVersion": 1, "alwaysOutputData": true, "webhookId": "60da32fe-2788-4eda-9ef0-3ba8ddd049cf", "credentials": {"githubApi": {"id": "Qtgltkkid3SAmtdp", "name": "GitHub account"}}, "continueOnFail": true}, {"parameters": {"assignments": {"assignments": [{"id": "6cf546c5-5737-4dbd-851b-17d68e0a3780", "name": "repo.owner", "type": "string", "value": "ottopan"}, {"id": "452efa28-2dc6-4ea3-a7a2-c35d100d0382", "name": "repo.name", "type": "string", "value": "N8N-Backup"}, {"id": "81c4dc54-86bf-4432-a23f-22c7ea831e74", "name": "repo.path", "type": "string", "value": "=workflows/{{ $json.tags[0].name }}"}]}, "options": {}}, "id": "4ac5e0f6-72ee-4f5b-8402-6d128ed2ad8a", "name": "Globals", "type": "n8n-nodes-base.set", "position": [820, 900], "typeVersion": 3.4}, {"parameters": {"content": "## Edit this node 👇", "height": 80, "width": 150, "color": 4}, "id": "4d12b637-98fe-4af6-8459-dc80eb540709", "name": "Sticky Note3", "type": "n8n-nodes-base.stickyNote", "position": [800, 780], "typeVersion": 1}, {"parameters": {"inputSource": "passthrough"}, "id": "8b563ca5-86f4-4602-8ead-e83443d68fb7", "name": "Execute Workflow Trigger", "type": "n8n-nodes-base.executeWorkflowTrigger", "position": [100, 1080], "typeVersion": 1.1}, {"parameters": {"workflowId": {"__rl": true, "mode": "id", "value": "={{ $workflow.id }}"}, "workflowInputs": {"value": {}, "schema": [], "mappingMode": "defineBelow", "matchingColumns": [], "attemptToConvertTypes": false, "convertFieldsToString": true}, "mode": "each", "options": {"waitForSubWorkflow": false}}, "id": "e841b1c0-d3b3-4afe-9382-6c82c04050d4", "name": "Execute Workflow", "type": "n8n-nodes-base.executeWorkflow", "position": [1120, 340], "typeVersion": 1.2}, {"parameters": {"assignments": {"assignments": [{"id": "12cad226-e091-4bbb-aed9-a8e01311772c", "name": "tags[0].name", "type": "string", "value": "={{ $('Execute Workflow Trigger').item.json.tags[0].name }}/"}]}, "options": {}}, "id": "16a90e3d-4e1c-403f-98ed-40745b18a9cb", "name": "/", "type": "n8n-nodes-base.set", "position": [580, 800], "typeVersion": 3.4}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"operator": {"type": "object", "operation": "exists", "singleValue": true}, "leftValue": "={{ $json.tags[0] }}", "rightValue": ""}]}, "renameOutput": true, "outputKey": "tag"}, {"conditions": {"options": {"version": 2, "leftValue": "", "caseSensitive": true, "typeValidation": "strict"}, "combinator": "and", "conditions": [{"id": "2656fbe3-fe35-4770-9c03-9a455ec618e4", "operator": {"type": "object", "operation": "notExists", "singleValue": true}, "leftValue": "={{ $json.tags[0] }}", "rightValue": ""}]}, "renameOutput": true, "outputKey": "none"}]}, "options": {}}, "id": "3e065f25-7412-4a6a-885e-5fb367c4bb85", "name": "tag?", "type": "n8n-nodes-base.switch", "position": [340, 900], "typeVersion": 3.2}], "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {"node:Schedule Trigger": {"recurrenceRules": []}}, "tags": [{"createdAt": "2025-06-15T00:36:53.781Z", "updatedAt": "2025-06-15T00:36:53.781Z", "id": "ZTgXd4fKFMPtLTPG", "name": "backup"}], "triggerCount": 1, "updatedAt": "2025-06-15T01:25:00.000Z", "versionId": "255f2683-8b34-497b-a202-b0e501c3c832"}