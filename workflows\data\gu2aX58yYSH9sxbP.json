{"active": false, "connections": {"When chat message received": {"main": [[{"node": "SerpApi", "type": "main", "index": 0}]]}, "SerpApi": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "createdAt": "2025-06-15T06:59:28.826Z", "id": "gu2aX58yYSH9sxbP", "isArchived": false, "meta": {"templateCredsSetupCompleted": true}, "name": "谷歌搜索趋势可视化", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [0, 0], "id": "4afdb2f2-78f0-4f50-aa3f-ebae8859d15e", "name": "When chat message received", "webhookId": "a824516c-89fa-4bd3-94c7-20dbd9c38a5b"}, {"parameters": {"operation": "google_trends", "q": "={{ $json.chatInput }}", "additionalFields": {}, "requestOptions": {}}, "type": "n8n-nodes-serpapi.serpApi", "typeVersion": 1, "position": [220, 0], "id": "4d476639-0e46-4457-aeb2-1575e165076c", "name": "SerpApi", "credentials": {"serpApi": {"id": "dz7qAtKdYrn6OxJW", "name": "SerpApi account"}}}, {"parameters": {"jsCode": "// The Code node in n8n typically receives input data via the 'items' array\n// and outputs data by returning an array of objects.\n// File system operations (fs.readFile, fs.writeFile) are not allowed in the n8n Code node.\n\nfunction transformJsonData(items) {\n    const dates = [];\n    const values = [];\n\n    // Assuming the input 'items' array contains objects with the structure of source.json\n    // Each item in 'items' represents an input item from the previous node.\n    items.forEach(item => {\n        // Access the JSON data of the current item\n        const jsonData = item.json;\n\n        if (jsonData && jsonData.interest_over_time && jsonData.interest_over_time.timeline_data) {\n            jsonData.interest_over_time.timeline_data.forEach(timelineItem => {\n                if (timelineItem.date && timelineItem.values && timelineItem.values.length > 0) {\n                    dates.push(timelineItem.date);\n                    values.push(timelineItem.values[0].extracted_value);\n                }\n            });\n        }\n    });\n\n    // The Code node expects an array of objects, where each object has a 'json' property\n    // containing the data for the output item.\n    // For the requested format, we return a single item with 'dates' and 'values' arrays.\n    return [\n        {\n            json: {\n                dates: dates,\n                values: values\n            }\n        }\n    ];\n}\n\n// When using this in an n8n Code node, you would typically call it like this:\nreturn transformJsonData(items);\n// The 'items' variable is automatically provided by n8n, containing the input data."}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, 0], "id": "36f5f3fa-d250-483d-a81e-25ecbc8d0157", "name": "Code"}, {"parameters": {"chartType": "line", "labelsMode": "array", "labelsArray": "={{ $json.dates }}", "data": "={{ $json.values }}", "chartOptions": {}, "datasetOptions": {}}, "type": "n8n-nodes-base.quick<PERSON><PERSON>", "typeVersion": 1, "position": [660, 0], "id": "34398174-efed-40dc-8830-267e85c4e3dc", "name": "<PERSON><PERSON><PERSON>"}], "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [{"createdAt": "2025-06-15T09:09:06.623Z", "updatedAt": "2025-06-15T09:09:06.623Z", "id": "xrCy2WqtdxJFKHHH", "name": "data"}], "triggerCount": 0, "updatedAt": "2025-06-15T09:09:18.000Z", "versionId": "6dd4e83d-3f8d-4220-930b-f919e12e2350"}