{"active": false, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Tavily": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "AI Agent": {"main": [[]]}}, "createdAt": "2025-06-15T14:11:09.251Z", "id": "vXR2gB1tElMRW0Pl", "isArchived": false, "meta": {"templateCredsSetupCompleted": true}, "name": "页面解析", "nodes": [{"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-580, -80], "id": "b4ff4252-8834-4fca-bb4b-6e4bc9b21574", "name": "When chat message received", "webhookId": "98425b07-9a67-435c-8fcc-c02849d9b738"}, {"parameters": {"options": {"systemMessage": "任务：页面解析\n- 基于用户给你的 URL，使用 Tavily 工具进行解析并总结\n- 总结输出要求如下\n1. 渐进式清单化输出的核心内容：{内容}，包含：一句话简述、核心要素可视化、核心功能、适用场景、关键技术、术语清单\n2. 言简意赅，句式结构简单，避免使用荣誉的形容词或副词\n3. 核心要素可视化：使用mermaid mindmap 最简单的语法构建要素图谱，最多3层\n4. 头部使用yaml格式，包含：title、source、desc、tags\n5. 标签最多5个，格式要求[AIGen/生成内容]\n\n## 输出模板：\n```\n---\ntitle: \"产品名称\"\nsource: \"产品官网链接”\ndesc: \"产品简述\"\ntags: AIGen/标签1,AIGen/标签2,\n---\n{内容}\n\n```"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-140, -80], "id": "ad8c7e24-40ff-40c6-9647-1052032ee04f", "name": "AI Agent"}, {"parameters": {"model": "google/gemini-2.5-flash-lite-preview-06-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-200, 120], "id": "4e735347-889c-4226-bf5d-141923b5a41a", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "5aFy16dqZKVDAgkF", "name": "OpenRouter account"}}}, {"parameters": {"resource": "extract", "urls": ["={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('urls0_URLs', `将对话中的链接作为变量值`, 'string') }}"], "options": {}}, "type": "@tavily/n8n-nodes-tavily.tavilyTool", "typeVersion": 1, "position": [20, 120], "id": "136201fc-0273-4551-9ce4-51fa22689cfc", "name": "<PERSON><PERSON>", "credentials": {"tavilyApi": {"id": "dfeSc0I1ylG6AW15", "name": "Tavily account"}}}, {"parameters": {"content": "# 输出模式2\n## 输出要求\n1. 渐进式清单化输出的核心内容：{内容}，包含：一句话简述、核心要素可视化、核心功能、适用场景、关键技术、术语清单\n2. 言简意赅，句式结构简单，避免使用荣誉的形容词或副词\n3. 核心要素可视化：使用mermaid mindmap 最简单的语法构建要素图谱，最多3层\n4. 头部使用yaml格式，包含：title、source、desc、tags\n5. 标签最多5个，格式要求[AIGen/生成内容]\n\n## 输出模板：\n```\n---\ntitle: \"产品名称\"\nsource: \"产品官网链接”\ndesc: \"产品简述\"\ntags: AIGen/标签1,AIGen/标签2,\n---\n{内容}\n\n```", "height": 500, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-600, -800], "id": "58cbfb1c-5adb-48f3-ab6d-cdfe8c5daa80", "name": "<PERSON><PERSON>"}], "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-06-20T23:02:43.000Z", "versionId": "281f268a-ade7-4bed-93df-7688ed98a67e"}