# N8N Workflow Sync Script
# Sync workflows directory between local and GitHub remote repository

param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("pull", "push", "sync", "status")]
    [string]$Action = "sync",
    
    [Parameter(Mandatory=$false)]
    [switch]$Force,
    
    [Parameter(Mandatory=$false)]
    [switch]$Backup,
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun
)

# Configuration variables
$REPO_ROOT = Get-Location
$WORKFLOWS_DIR = Join-Path $REPO_ROOT "workflows"
$BACKUP_DIR = Join-Path $REPO_ROOT ".backups"
$REMOTE_NAME = "origin"
$BRANCH_NAME = "main"

# Color output function
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    switch ($Color) {
        "Red" { Write-Host $Message -ForegroundColor Red }
        "Green" { Write-Host $Message -ForegroundColor Green }
        "Yellow" { Write-Host $Message -ForegroundColor Yellow }
        "Blue" { Write-Host $Message -ForegroundColor Blue }
        "Cyan" { Write-Host $Message -ForegroundColor Cyan }
        default { Write-Host $Message }
    }
}

# 检查Git状态
function Test-GitRepository {
    if (-not (Test-Path ".git")) {
        Write-ColorOutput "错误: 当前目录不是Git仓库" "Red"
        exit 1
    }
    
    $gitStatus = git status --porcelain
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "错误: 无法获取Git状态" "Red"
        exit 1
    }
    
    return $gitStatus
}

# 创建备份
function New-Backup {
    if (-not $Backup) { return }
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $backupPath = Join-Path $BACKUP_DIR "workflows_$timestamp"
    
    if (-not (Test-Path $BACKUP_DIR)) {
        New-Item -ItemType Directory -Path $BACKUP_DIR -Force | Out-Null
    }
    
    if (Test-Path $WORKFLOWS_DIR) {
        Write-ColorOutput "创建备份: $backupPath" "Blue"
        Copy-Item -Path $WORKFLOWS_DIR -Destination $backupPath -Recurse -Force
    }
}

# 检查远程连接
function Test-RemoteConnection {
    Write-ColorOutput "检查远程连接..." "Blue"
    $result = git ls-remote --heads $REMOTE_NAME 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "错误: 无法连接到远程仓库" "Red"
        Write-ColorOutput $result "Red"
        exit 1
    }
    Write-ColorOutput "远程连接正常" "Green"
}

# 拉取远程更改
function Invoke-Pull {
    Write-ColorOutput "从远程拉取更改..." "Blue"
    
    if ($DryRun) {
        Write-ColorOutput "[DRY RUN] 将执行: git fetch $REMOTE_NAME" "Yellow"
        Write-ColorOutput "[DRY RUN] 将执行: git merge $REMOTE_NAME/$BRANCH_NAME" "Yellow"
        return
    }
    
    git fetch $REMOTE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "错误: 拉取失败" "Red"
        exit 1
    }
    
    $mergeResult = git merge $REMOTE_NAME/$BRANCH_NAME 2>&1
    if ($LASTEXITCODE -ne 0) {
        Write-ColorOutput "警告: 合并时发生冲突" "Yellow"
        Write-ColorOutput $mergeResult "Yellow"
        Write-ColorOutput "请手动解决冲突后重新运行同步" "Yellow"
        exit 1
    }
    
    Write-ColorOutput "拉取完成" "Green"
}

# 推送本地更改
function Invoke-Push {
    $gitStatus = Test-GitRepository
    
    if (-not $gitStatus -and -not $Force) {
        Write-ColorOutput "没有需要推送的更改" "Green"
        return
    }
    
    Write-ColorOutput "推送本地更改..." "Blue"
    
    if ($DryRun) {
        Write-ColorOutput "[DRY RUN] 将添加的文件:" "Yellow"
        git diff --name-only HEAD
        return
    }
    
    # 只添加workflows目录下的.json文件
    $jsonFiles = Get-ChildItem -Path $WORKFLOWS_DIR -Recurse -Filter "*.json" -ErrorAction SilentlyContinue
    
    if ($jsonFiles) {
        foreach ($file in $jsonFiles) {
            $relativePath = $file.FullName.Replace($REPO_ROOT + "\", "").Replace("\", "/")
            git add $relativePath
        }
        
        $commitMessage = "自动同步工作流 - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
        git commit -m $commitMessage
        
        if ($LASTEXITCODE -eq 0) {
            git push $REMOTE_NAME $BRANCH_NAME
            if ($LASTEXITCODE -eq 0) {
                Write-ColorOutput "推送完成" "Green"
            } else {
                Write-ColorOutput "错误: 推送失败" "Red"
                exit 1
            }
        }
    } else {
        Write-ColorOutput "没有找到需要同步的.json文件" "Yellow"
    }
}

# 显示状态
function Show-Status {
    Write-ColorOutput "=== N8N工作流同步状态 ===" "Cyan"
    
    # Git状态
    Write-ColorOutput "`n本地Git状态:" "Blue"
    git status --short
    
    # 远程比较
    Write-ColorOutput "`n与远程的差异:" "Blue"
    git fetch $REMOTE_NAME --quiet
    $ahead = git rev-list --count HEAD..$REMOTE_NAME/$BRANCH_NAME
    $behind = git rev-list --count $REMOTE_NAME/$BRANCH_NAME..HEAD
    
    if ($ahead -eq "0" -and $behind -eq "0") {
        Write-ColorOutput "✓ 本地与远程同步" "Green"
    } else {
        if ($ahead -gt 0) {
            Write-ColorOutput "↓ 远程领先 $ahead 个提交" "Yellow"
        }
        if ($behind -gt 0) {
            Write-ColorOutput "↑ 本地领先 $behind 个提交" "Yellow"
        }
    }
    
    # 工作流文件统计
    Write-ColorOutput "`n工作流文件统计:" "Blue"
    if (Test-Path $WORKFLOWS_DIR) {
        $jsonCount = (Get-ChildItem -Path $WORKFLOWS_DIR -Recurse -Filter "*.json" | Measure-Object).Count
        Write-ColorOutput "总计: $jsonCount 个工作流文件" "Green"
        
        # 按目录分组
        $dirs = Get-ChildItem -Path $WORKFLOWS_DIR -Directory -ErrorAction SilentlyContinue
        foreach ($dir in $dirs) {
            $dirJsonCount = (Get-ChildItem -Path $dir.FullName -Recurse -Filter "*.json" | Measure-Object).Count
            Write-ColorOutput "  $($dir.Name): $dirJsonCount 个文件" "White"
        }
    } else {
        Write-ColorOutput "workflows目录不存在" "Red"
    }
}

# 完整同步
function Invoke-Sync {
    Write-ColorOutput "开始完整同步..." "Cyan"
    
    New-Backup
    Test-RemoteConnection
    
    # 先拉取远程更改
    Invoke-Pull
    
    # 再推送本地更改
    Invoke-Push
    
    Write-ColorOutput "同步完成!" "Green"
}

# 主逻辑
try {
    Write-ColorOutput "N8N工作流同步工具" "Cyan"
    Write-ColorOutput "操作: $Action" "Blue"
    
    if ($DryRun) {
        Write-ColorOutput "*** 试运行模式 - 不会执行实际操作 ***" "Yellow"
    }
    
    switch ($Action) {
        "pull" { 
            Test-RemoteConnection
            Invoke-Pull 
        }
        "push" { 
            Test-RemoteConnection
            New-Backup
            Invoke-Push 
        }
        "sync" { 
            Invoke-Sync 
        }
        "status" { 
            Show-Status 
        }
    }
    
} catch {
    Write-ColorOutput "发生错误: $($_.Exception.Message)" "Red"
    exit 1
}
