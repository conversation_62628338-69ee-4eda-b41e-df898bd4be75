{"active": false, "connections": {"When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenRouter Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "AI Agent": {"main": [[]]}, "Tavily": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Create a database page in Notion": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}}, "createdAt": "2025-07-04T12:33:53.856Z", "id": "QcvVrR06IiEd8znV", "isArchived": false, "meta": {"templateCredsSetupCompleted": true}, "name": "页面解析-自定notion模板", "nodes": [{"parameters": {"options": {}, "path": "125d2fe2-36e1-4b32-8d10-72ff88cdbdbf"}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [-580, -80], "id": "8cea1b47-8182-47cb-b3f3-17f2bee90f57", "name": "When chat message received", "webhookId": "125d2fe2-36e1-4b32-8d10-72ff88cdbdbf"}, {"parameters": {"options": {"systemMessage": "## 任务核心描述：页面解析+写入Notion\n1. 页面解析：基于用户给你的 URL，使用 Tavily 工具进行解析并总结，默认使用中文，除非碰到专有名词\n\n2. 总结输出要求如下\n- 渐进式清单化输出，包含：一句话简述、核心要素可视化、核心功能（最多5条）、适用场景（最多5条）、术语清单（最多5条）\n- 标签至少3个\n- 言简意赅，句式结构简单，避免使用冗余的形容词或副词，面向小白解读专业性内容\n- 核心要素图谱：使用mermaid mindmap 最简单的语法构建要素图谱，最多3层\n- 头部使用yaml格式，包含：title、source、desc、tags\n- 标签最多5个，格式要求[AIGen/生成内容]\n\n3. 总结后调用并写入 Notion\n- 使用产品名称写入：Title\n- 写入 Properties，Properties和 yaml 的对应关系：name=title；tag=tags；url=source；description=desc\n- 内容写入Blocks\n\n\n## 输出模板：\n---\ntitle: \"产品名称\"\nsource: \"产品官网链接”\ndesc: \"产品简述\"\ntags: AIGen/标签1,AIGen/标签2,\n---\n### 一句话简述\n{一句话简述的内容}\n\n### 要素图谱\n{mindmap内容}\n\n### 适用场景\n{适用场景的内容}\n\n### 术语清单\n- 术语1：定义+解读\n- 术语2：定义+解读\n- 术语X：定义+解读\n\n## mermaid 示例\n```mermaid\nmindmap\n  root((产品名称))\n    Element1\n      Element1-1\n      Element1-2\n      Element1-X\n    Element2\n      Element2-1\n      Element2-2\n        Element2-2-1\n        Element2-2-2\n    ElementX\n```"}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [-320, -80], "id": "4fc70557-9f0e-4ab2-a05c-c539fa30ab5d", "name": "AI Agent"}, {"parameters": {"model": "google/gemini-2.5-flash-lite-preview-06-17", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "typeVersion": 1, "position": [-360, 140], "id": "e09b64f7-606d-4d15-b272-a93fee4d99ae", "name": "OpenRouter <PERSON>", "credentials": {"openRouterApi": {"id": "5aFy16dqZKVDAgkF", "name": "OpenRouter account"}}}, {"parameters": {"content": "# 输出模式2\n## 输出要求\n1. 渐进式清单化输出的核心内容：{内容}，包含：一句话简述、核心要素可视化、核心功能、适用场景、关键技术、术语清单\n2. 言简意赅，句式结构简单，避免使用荣誉的形容词或副词\n3. 核心要素可视化：使用mermaid mindmap 最简单的语法构建要素图谱，最多3层\n4. 头部使用yaml格式，包含：title、source、desc、tags\n5. 标签最多5个，格式要求[AIGen/生成内容]\n\n## 输出模板：\n```\n---\ntitle: \"产品名称\"\nsource: \"产品官网链接”\ndesc: \"产品简述\"\ntags: AIGen/标签1,AIGen/标签2,\n---\n{内容}\n\n```", "height": 500, "width": 540}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-600, -800], "id": "3cd1389d-def5-4db9-b113-746bedcdb445", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## 任务核心描述：页面解析+写入Notion\n1. 页面解析：基于用户给你的 URL，使用 Tavily 工具进行解析并总结，默认使用中文，除非碰到专有名词\n\n2. 总结输出要求如下\n- 渐进式清单化输出，包含：一句话简述、核心要素可视化、核心功能（最多5条）、适用场景（最多5条）、术语清单（最多5条）\n- 标签至少3个\n- 言简意赅，句式结构简单，避免使用冗余的形容词或副词，面向小白解读专业性内容\n- 核心要素图谱：使用mermaid mindmap 最简单的语法构建要素图谱，最多3层\n- 头部使用yaml格式，包含：title、source、desc、tags\n- 标签最多5个，格式要求[AIGen/生成内容]\n\n3. 总结后调用并写入 Notion\n- 使用产品名称写入：Title\n- 写入 Properties，Properties和 yaml 的对应关系：name=title；tag=tags；url=source；description=desc\n- 内容写入Blocks\n\n\n## 输出模板：\n---\ntitle: \"产品名称\"\nsource: \"产品官网链接”\ndesc: \"产品简述\"\ntags: AIGen/标签1,AIGen/标签2,\n---\n### 一句话简述\n{一句话简述的内容}\n\n### 要素图谱\n{mindmap内容}\n\n### 适用场景\n{适用场景的内容}\n\n### 术语清单\n- 术语1：定义+解读\n- 术语2：定义+解读\n- 术语X：定义+解读\n\n## mermaid 示例\n```mermaid\nmindmap\n  root((产品名称))\n    Element1\n      Element1-1\n      Element1-2\n      Element1-X\n    Element2\n      Element2-1\n      Element2-2\n        Element2-2-1\n        Element2-2-2\n    ElementX\n```", "height": 1520, "width": 640}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [520, -900], "id": "7229f300-f25b-46a1-99f9-b30dd6d3ee5f", "name": "Sticky Note1"}, {"parameters": {"resource": "extract", "urls": ["={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('urls0_URLs', `将对话中的链接作为变量值`, 'string') }}"], "options": {}}, "type": "@tavily/n8n-nodes-tavily.tavilyTool", "typeVersion": 1, "position": [-240, 140], "id": "96b3245a-0fee-4163-b78c-cc884d73711e", "name": "<PERSON><PERSON>", "credentials": {"tavilyApi": {"id": "dfeSc0I1ylG6AW15", "name": "Tavily account"}}}, {"parameters": {"resource": "databasePage", "databaseId": {"__rl": true, "value": "22325dc1-f7a9-80d5-968b-f3bc265148fb", "mode": "list", "cachedResultName": "产品", "cachedResultUrl": "https://www.notion.so/22325dc1f7a980d5968bf3bc265148fb"}, "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('Title', ``, 'string') }}", "propertiesUi": {"propertyValues": [{"key": "name|title", "title": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('propertyValues0_Title', ``, 'string') }}"}, {"key": "tag|multi_select", "multiSelectValue": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('propertyValues1_Option_Names_or_IDs', ``, 'string') }}"}, {"key": "description|rich_text", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('propertyValues2_Text', ``, 'string') }}"}, {"key": "url|url", "urlValue": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('propertyValues3_URL', ``, 'string') }}"}]}, "blockUi": {"blockValues": [{"type": "heading_3", "textContent": "一句话简述"}, {"textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues1_Text', `一句话简述的内容`, 'string') }}"}, {"type": "heading_3", "textContent": "要素图谱"}, {"textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues3_Text', `要素图谱的内容`, 'string') }}"}, {"type": "heading_3", "textContent": "核心功能"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues5_Text', `核心功能内容1`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues6_Text', `核心功能内容2`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues7_Text', `核心功能内容3`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues8_Text', `核心功能内容4`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues9_Text', `核心功能内容5`, 'string') }}"}, {"type": "heading_3", "textContent": "适用场景"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues11_Text', `适用场景内容1`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues12_Text', `适用场景内容2`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues13_Text', `适用场景内容3`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues14_Text', `适用场景内容4`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues15_Text', `适用场景内容5`, 'string') }}"}, {"type": "heading_3", "textContent": "术语清单"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues17_Text', `术语清单内容1`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues18_Text', `术语清单内容2`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues19_Text', `术语清单内容3`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues20_Text', `术语清单内容4`, 'string') }}"}, {"type": "bulleted_list_item", "textContent": "={{ /*n8n-auto-generated-fromAI-override*/ $fromAI('blockValues21_Text', `术语清单内容5`, 'string') }}"}]}, "options": {}}, "type": "n8n-nodes-base.notionTool", "typeVersion": 2.2, "position": [-120, 140], "id": "28d58997-4aa8-4904-8bce-a0e1249bae6d", "name": "Create a database page in Notion", "credentials": {"notionApi": {"id": "4VJhI50wiIeKMsZB", "name": "Notion account"}}}], "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": null, "tags": [], "triggerCount": 0, "updatedAt": "2025-07-04T12:33:53.856Z", "versionId": "b56b8f9e-83cc-436f-b90a-0ef4fe061beb"}