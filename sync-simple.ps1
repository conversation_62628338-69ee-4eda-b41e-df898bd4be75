# Simple N8N Workflow Sync Script
param(
    [Parameter(Mandatory=$false)]
    [ValidateSet("pull", "push", "sync", "status")]
    [string]$Action = "sync"
)

# Configuration
$REMOTE_NAME = "origin"
$BRANCH_NAME = "main"

function Write-Info {
    param([string]$Message)
    Write-Host $Message -ForegroundColor Cyan
}

function Write-Success {
    param([string]$Message)
    Write-Host $Message -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host $Message -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host $Message -ForegroundColor Red
}

function Test-GitRepo {
    if (-not (Test-Path ".git")) {
        Write-Error "Error: Not a Git repository"
        exit 1
    }
}

function Get-GitStatus {
    Write-Info "=== Git Status ==="
    git status --short
    
    Write-Info "`n=== Remote Comparison ==="
    git fetch $REMOTE_NAME --quiet 2>$null
    
    $ahead = git rev-list --count HEAD..$REMOTE_NAME/$BRANCH_NAME 2>$null
    $behind = git rev-list --count $REMOTE_NAME/$BRANCH_NAME..HEAD 2>$null
    
    if ($ahead -eq "0" -and $behind -eq "0") {
        Write-Success "Local and remote are in sync"
    } else {
        if ($ahead -gt 0) {
            Write-Warning "Remote is ahead by $ahead commits"
        }
        if ($behind -gt 0) {
            Write-Warning "Local is ahead by $behind commits"
        }
    }
    
    Write-Info "`n=== Workflow Files ==="
    if (Test-Path "workflows") {
        $jsonFiles = Get-ChildItem -Path "workflows" -Recurse -Filter "*.json"
        Write-Success "Total workflow files: $($jsonFiles.Count)"
    } else {
        Write-Warning "workflows directory not found"
    }
}

function Invoke-Pull {
    Write-Info "Pulling from remote..."
    
    git fetch $REMOTE_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to fetch from remote"
        exit 1
    }
    
    git merge $REMOTE_NAME/$BRANCH_NAME
    if ($LASTEXITCODE -ne 0) {
        Write-Warning "Merge conflicts detected. Please resolve manually."
        exit 1
    }
    
    Write-Success "Pull completed successfully"
}

function Invoke-Push {
    $status = git status --porcelain
    
    if (-not $status) {
        Write-Success "No changes to push"
        return
    }
    
    Write-Info "Pushing local changes..."
    
    # Add all workflow files
    git add workflows/
    
    $commitMessage = "Auto sync workflows - $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    git commit -m $commitMessage
    
    if ($LASTEXITCODE -eq 0) {
        git push $REMOTE_NAME $BRANCH_NAME
        if ($LASTEXITCODE -eq 0) {
            Write-Success "Push completed successfully"
        } else {
            Write-Error "Failed to push to remote"
            exit 1
        }
    } else {
        Write-Warning "Nothing to commit"
    }
}

function Invoke-Sync {
    Write-Info "Starting full sync..."
    
    # First pull remote changes
    Invoke-Pull
    
    # Then push local changes
    Invoke-Push
    
    Write-Success "Sync completed!"
}

# Main execution
try {
    Test-GitRepo
    
    Write-Info "N8N Workflow Sync Tool"
    Write-Info "Action: $Action"
    Write-Info ""
    
    switch ($Action) {
        "pull" { Invoke-Pull }
        "push" { Invoke-Push }
        "sync" { Invoke-Sync }
        "status" { Get-GitStatus }
    }
    
} catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    exit 1
}
