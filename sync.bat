@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: N8N工作流同步快捷脚本
:: 使用方法: sync.bat [pull|push|sync|status]

set "ACTION=%1"
if "%ACTION%"=="" set "ACTION=sync"

echo.
echo ================================
echo    N8N工作流同步工具
echo ================================
echo.

:: 检查PowerShell是否可用
powershell -Command "Get-Host" >nul 2>&1
if errorlevel 1 (
    echo 错误: 需要PowerShell支持
    pause
    exit /b 1
)

:: 执行PowerShell脚本
echo 执行操作: %ACTION%
echo.

powershell -ExecutionPolicy Bypass -File "sync-workflows.ps1" -Action %ACTION% -Backup

if errorlevel 1 (
    echo.
    echo 同步过程中发生错误，请检查上述输出信息
    pause
    exit /b 1
)

echo.
echo 操作完成!
if "%ACTION%"=="sync" (
    echo 建议运行 'sync.bat status' 查看当前状态
)
echo.
pause
